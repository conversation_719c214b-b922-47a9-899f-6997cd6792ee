<template>
  <div class="table-container">
    <div class="toolbar">
      <div class="toolbar-actions">
        <a-button type="primary" @click="submit">提交</a-button>
      </div>
      <div class="toolbar-filters">
        <span>日期范围:</span>
        <a-date-picker v-model:value="dateRange" @change="dateRangeChange" :allowClear="false" />
      </div>
    </div>
    <div class="flex justify-between items-center">
      <div></div>
      <div class="title items-center">
        <div>{{ dayjs(dateRange).format('YYYY年MM月DD日') }}模拟利润</div>
        <div v-show="!isSave" class="text-red">*</div>
      </div>
      <div class="operator">
        <a-tooltip>
          <template #title>重新拉取</template>
          <ToolOutlined class="mr-6 text-lg" @click="autoFill" />
        </a-tooltip>
        <div class="mr-10"></div>
      </div>
    </div>
    <div class="grid-table">
      <!-- 表头 -->
      <div class="row header">
        <div>
          <div></div>
          <div></div>
          <div></div>
          <div></div>
          <div class="span-3">铜精矿</div>
          <div></div>
          <div></div>
<!--          <div></div>-->
        </div>
        <div>
          <div>序号</div>
          <div>项目</div>
          <div>单位</div>
          <div>合计</div>
          <div>含铜</div>
          <div>含金</div>
          <div>含银</div>
          <div>含硫</div>
          <div>硫精矿（折标35%）</div>
          <div>钼精矿</div>
<!--          <div>其他</div>-->
        </div>
      </div>
      <!-- 数据行 -->
      <div class="row">
        <!--   序号   -->
        <div>1</div>
        <!--   项目     -->
        <div>金属实际价格（含税）</div>
        <!--   单位     -->
        <div>元/吨(公斤)</div>
        <!--   合计     -->
        <div></div>
        <!--   含铜     -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="tData.jg" /></div>
        <!--   含金     -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="jData.jg" /></div>
        <!--    含银    -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="yData.jg" /></div>
        <!--    含硫    -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="lData.jg" /></div>
        <!--    硫精矿（折标35%）    -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="ljkData.jg" /></div>
        <!--    钼精矿    -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="mjkData.jg" /></div>
        <!--      其他        -->
<!--        <div></div>-->
      </div>
      <div class="row">
        <!--   序号   -->
        <div>2</div>
        <!--   项目     -->
        <div>公司实际结算系数</div>
        <!--   单位     -->
        <div>%</div>
        <!--   合计     -->
        <div></div>
        <!--   含铜     -->
        <div>{{ formatNumber(tData.xs * 100) }}%</div>
        <!--   含金     -->
        <div>{{ formatNumber(jData.xs * 100) }}%</div>
        <!--    含银    -->
        <div>{{ formatNumber(yData.xs * 100) }}%</div>
        <!--    含硫    -->
        <div>{{ formatNumber(lData.xs * 100) }}%</div>
        <!--    硫精矿（折标35%）    -->
        <div>{{ formatNumber(ljkData.xs * 100) }}%</div>
        <!--    钼精矿    -->
        <div>{{ formatNumber(mjkData.xs * 100) }}%</div>
        <!--      其他        -->
        <div></div>
      </div>
      <div class="row">
        <!--   序号   -->
        <div>3</div>
        <!--   项目     -->
        <div>实际销量</div>
        <!--   单位     -->
        <div>吨(公斤)</div>
        <!--   合计     -->
        <div></div>
        <!--   含铜     -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="tData.xl" /></div>
        <!--   含金     -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="jData.xl" /></div>
        <!--    含银    -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="yData.xl" /></div>
        <!--    含硫    -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="lData.xl" /></div>
        <!--    硫精矿（折标35%）    -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="ljkData.xl" /></div>
        <!--    钼精矿    -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="mjkData.xl" /></div>
        <!--      其他        -->
        <div></div>
      </div>
      <div class="row">
        <!--   序号   -->
        <div>4</div>
        <!--   项目     -->
        <div>实际销售收入</div>
        <!--   单位     -->
        <div>万元</div>
        <!--   合计     -->
        <div>{{ formatNumber(slSum) }}</div>
        <!--   含铜     -->
        <div>{{ formatNumber((tData.jg * tData.xs * tData.xl) / 1.13 / 10000) }}</div>
        <!--   含金     -->
        <div>{{ formatNumber((jData.jg * jData.xs * jData.xl) / 1.13 / 10000) }}</div>
        <!--    含银    -->
        <div>{{ formatNumber((yData.jg * yData.xs * yData.xl) / 1.13 / 10000) }}</div>
        <!--    含硫    -->
        <div>{{ formatNumber((lData.jg * lData.xs * lData.xl) / 1.13 / 10000) }}</div>
        <!--    硫精矿（折标35%）    -->
        <div>{{ formatNumber((ljkData.jg * ljkData.xs * ljkData.xl) / 1.13 / 10000) }}</div>
        <!--    钼精矿    -->
        <div>{{ formatNumber((mjkData.jg * mjkData.xs * mjkData.xl) / 1.13 / 10000) }}</div>
        <!--      其他        -->
        <div></div>
      </div>
      <div class="row">
        <!--   序号   -->
        <div>5</div>
        <!--   项目     -->
        <div>金属总成本</div>
        <!--   单位     -->
        <div>万元</div>
        <!--   合计     -->
        <div>{{ formatNumber(cbSum) }}</div>
        <!--   含铜     -->
        <div>{{ formatNumber(tData.cb) }}</div>
        <!--   含金     -->
        <div>{{ formatNumber(jData.cb) }}</div>
        <!--    含银    -->
        <div>{{ formatNumber(yData.cb) }}</div>
        <!--    含硫    -->
        <div>{{ formatNumber(lData.cb) }}</div>
        <!--    硫精矿（折标35%）    -->
        <div>{{ formatNumber(ljkData.cb) }}</div>
        <!--    钼精矿    -->
        <div>{{ formatNumber(mjkData.cb) }}</div>
        <!--      其他        -->
        <div></div>
      </div>
      <div class="row">
        <!--   序号   -->
        <div>6</div>
        <!--   项目     -->
        <div>其他费用等</div>
        <!--   单位     -->
        <div>万元</div>
        <!--   合计     -->
        <div class="span-8">合计：{{ formatNumber(pageData.qtfy) }}</div>
      </div>
      <div class="row">
        <!--   序号   -->
        <div>7</div>
        <!--   项目     -->
        <div>分公司及多经单位利润</div>
        <!--   单位     -->
        <div>万元</div>
        <!--   合计     -->
        <div class="span-8">合计：{{ formatNumber(pageData.frdw) }}</div>
      </div>
      <div class="row">
        <!--   序号   -->
        <div>8</div>
        <!--   项目     -->
        <div>全矿模拟利润</div>
        <!--   单位     -->
        <div>万元</div>
        <!--   合计     -->
        <div class="span-8">合计：{{ formatNumber(slSum - cbSum - pageData.qtfy + pageData.frdw) }}</div>
      </div>
      <div class="row">
        <!--   序号   -->
        <div>9</div>
        <!--   项目     -->
        <div>公司进度计划</div>
        <!--   单位     -->
        <div>万元</div>
        <!--   合计     -->
        <div class="span-8" @click="handleJhClick">合计：{{ formatNumber(pageData.jh / 12 / daysInMonth) }}</div>
      </div>
      <div class="row">
        <!--   序号   -->
        <div>10</div>
        <!--   项目     -->
        <div>与进度计划比</div>
        <!--   单位     -->
        <div>万元</div>
        <!--   合计     -->
        <div class="span-8">合计：{{ formatNumber(slSum - cbSum - pageData.qtfy + pageData.frdw - pageData.jh / 12 / daysInMonth) }}</div>
      </div>
    </div>
  </div>
  <!-- 公司进度计划值输入模态框 -->
  <JhInputModal @register="registerJhModal" @success="handleJhSuccess" />
</template>
<script lang="ts" setup name="cw-mnlr-day">
  import { computed, onMounted, ref, watch } from 'vue';
  // 项目字典
  import { defHttp } from '@/utils/http/axios';
  import dayjs, { Dayjs } from 'dayjs';
  import Big from 'big.js';
  import { formatNumber, getSafeValue } from '@/utils/showUtils';
  import { ToolOutlined } from '@ant-design/icons-vue';
  import { useModal } from '/@/components/Modal';
  import JhInputModal from '../components/JhInputModal.vue';

  // 获取数据
  const pageData: any = ref({});
  const jData: any = ref({});
  const yData: any = ref({});
  const tData: any = ref({});
  const lData: any = ref({});
  const ljkData: any = ref({});
  const mjkData: any = ref({});
  const show = ref(false);
  let oldData: String = '';
  let isSave = ref(true);
  let daysInMonth = ref(0);

  // 注册模态框
  const [registerJhModal, { openModal: openJhModal }] = useModal();

  // 处理公司进度计划点击事件
  const handleJhClick = () => {
    openJhModal(true, {
      jh: pageData.value.jh,
    });
  };

  // 处理模态框提交成功
  const handleJhSuccess = (values) => {
    if (values && values.jh) {
      pageData.value.jh = values.jh;
    }
  };

  // 日期选择
  // 默认选择上一天（昨天）
  const dateRange = ref(dayjs().add(-1, 'd'));
  onMounted(async () => {
    await httpGetData(dateRange.value);
  });
  // 是否保存
  let firstWatch = true;
  watch(
    () => [jData.value, yData.value, tData.value, lData.value, ljkData.value, mjkData.value],
    () => {
      if (firstWatch) {
        oldData = JSON.stringify([jData.value, yData.value, tData.value, lData.value, ljkData.value, mjkData.value]);
        firstWatch = false;
        return;
      }
      isSave.value = oldData === JSON.stringify([jData.value, yData.value, tData.value, lData.value, ljkData.value, mjkData.value]);
    },
    { deep: true }
  );
  // 日期选择器变化
  const dateRangeChange = async (v) => {
    await httpGetData(dayjs(v));
  };
  console.log(new Big('0.1').plus(new Big('0.2')).toNumber());
  // 计算属性
  const slSum = computed(() => {
    const tJg = getSafeValue(tData.value.jg);
    const tXs = getSafeValue(tData.value.xs);
    const tXl = getSafeValue(tData.value.xl);
    const yJg = getSafeValue(yData.value.jg);
    const yXs = getSafeValue(yData.value.xs);
    const yXl = getSafeValue(yData.value.xl);
    const jJg = getSafeValue(jData.value.jg);
    const jXs = getSafeValue(jData.value.xs);
    const jXl = getSafeValue(jData.value.xl);
    const lJg = getSafeValue(lData.value.jg);
    const lXs = getSafeValue(lData.value.xs);
    const lXl = getSafeValue(lData.value.xl);
    const ljkJg = getSafeValue(ljkData.value.jg);
    const ljkXs = getSafeValue(ljkData.value.xs);
    const ljkXl = getSafeValue(ljkData.value.xl);
    const mjkJg = getSafeValue(mjkData.value.jg);
    const mjkXs = getSafeValue(mjkData.value.xs);
    const mjkXl = getSafeValue(mjkData.value.xl);

    const tValue = tJg.times(tXs).times(tXl).div('1.13').div('10000');
    const yValue = yJg.times(yXs).times(yXl).div('1.13').div('10000');
    const jValue = jJg.times(jXs).times(jXl).div('1.13').div('10000');
    const lValue = lJg.times(lXs).times(lXl).div('1.13').div('10000');
    const ljkValue = ljkJg.times(ljkXs).times(ljkXl).div('1.13').div('10000');
    const mjkValue = mjkJg.times(mjkXs).times(mjkXl).div('1.13').div('10000');

    return tValue.plus(yValue).plus(jValue).plus(lValue).plus(ljkValue).plus(mjkValue).toNumber();
  });
  let cbSum = ref(0);

  // 提交
  const submit = async () => {
    // 提交数据
    await defHttp.post({
      url: 'mnlr/cwMnlrDay/submit',
      params: {
        submitDate: dayjs(dateRange.value).format('YYYY-MM-DD'),
        rows: [jData.value, yData.value, tData.value, lData.value, ljkData.value],
        qtfy: pageData.value.qtfy,
        frdw: pageData.value.frdw,
        jh: pageData.value.jh,
      },
    });
    let mnlr = getSafeValue(slSum.value)
      .minus(getSafeValue(cbSum.value))
      .minus(getSafeValue(pageData.value.qtfy))
      .plus(getSafeValue(pageData.value.frdw))
      .toNumber();
    let jhb = getSafeValue(slSum.value)
      .minus(getSafeValue(cbSum.value))
      .minus(getSafeValue(pageData.value.qtfy))
      .plus(getSafeValue(pageData.value.frdw))
      .minus(getSafeValue(pageData.value.jh).div(12))
      .toNumber();
    await defHttp.post(
      {
        url: 'mnlr/cwMnlrStatisticsDay/addOrEdit',
        params: {
          sl: slSum.value,
          cb: cbSum.value,
          djlr: pageData.value.frdw,
          qt: pageData.value.qtfy,
          mnlr: mnlr,
          gsjh: getSafeValue(pageData.value.jh).div(12),
          jhb: jhb,
          recordTime: dayjs(dateRange.value).format('YYYY-MM-DD'),
          unit: '万元',
        },
      },
      { successMessageMode: 'none' }
    );

    httpGetData(dateRange.value);
  };

  // 自动填充并自动提交
  const autoFill = async () => {
    await defHttp.get({
      url: 'mnlr/cwMnlrDay/autoFill',
      params: { queryDate: dayjs(dateRange.value).format('YYYY-MM-DD') },
    });
    // 重新获取数据并提交
    await httpGetData(dateRange.value);
    await submit();
  };

  // =====================================公共函数
  const httpGetData = async (date: Dayjs) => {
    show.value = false;
    let queryDate = dayjs(date).format('YYYY-MM-DD');
    daysInMonth.value = dayjs(queryDate).daysInMonth();
    const res = await defHttp.get({ url: 'mnlr/cwMnlrDay/query', params: { queryDate } });
    pageData.value = {
      qtfy: getSafeValue(res.qtfy).toNumber(),
      frdw: getSafeValue(res.frdw).toNumber(),
      jh: getSafeValue(res.jh).toNumber(),
    };
    cbSum.value = res.zcb;
    jData.value = res.rows.filter((item) => item.type == 'j')[0];
    yData.value = res.rows.filter((item) => item.type == 'y')[0];
    tData.value = res.rows.filter((item) => item.type == 't')[0];
    lData.value = res.rows.filter((item) => item.type == 'l')[0];
    ljkData.value = res.rows.filter((item) => item.type == 'ljk')[0];
    mjkData.value = res.rows.filter((item) => item.type == 'mjk')[0];

    oldData = JSON.stringify([jData.value, yData.value, tData.value, lData.value, ljkData.value, mjkData.value]);
    isSave.value = true;
    show.value = true;
  };
</script>
<style lang="less" scoped>
  @import url(@/views/cw/public/style/ant-input.css);

  :deep(.custom-center-text) {
    width: 70%;
  }

  /* 容器样式 */
  .table-container {
    width: 100%;
    //max-width: 1500px;
    margin: 0 auto;
    background: linear-gradient(180deg, #f5f7fa 0%, #eef1f5 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08);
  }

  /* 工具栏样式 */
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #eaeaea;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  /* 工具栏动作区域 */
  .toolbar-actions {
    display: flex;
    gap: 10px;
  }

  /* 提交按钮样式 */
  .submit-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .submit-button:hover {
    background-color: #0056b3;
  }

  /* 工具栏筛选区域 */
  .toolbar-filters {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .toolbar-filters span {
    font-weight: bold;
  }

  /* 时间选择器样式 */
  .ant-calendar-picker-input.ant-input {
    height: 36px;
    padding: 0 10px;
    font-size: 14px;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .ant-calendar-picker-input.ant-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
  }

  /* 容器样式 */
  .table-container {
    width: 100%;
    //max-width: 1800px;
    margin: 0 auto;
    background: linear-gradient(180deg, #f5f7fa 0%, #eef1f5 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08);
  }

  /* 标题样式 */
  .title {
    display: flex;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    text-align: center;
    padding: 15px 0;
  }

  /* 表格基础样式 */
  .grid-table {
    display: grid;
    gap: 5px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #ffffff; /* 确保表格内部有清晰对比 */
  }

  /* 行样式 */
  .row {
    display: contents; /* 让子元素直接成为网格项 */

    .span-7 {
      grid-column: span 7;
      background-color: #e6ffed;
      color: #28a745;
      font-weight: bold;
    }
  }

  .custom-center-text :deep(.ant-input-number-input) {
    text-align: center !important;
  }

  :deep(.custom-center-text) {
    text-align: center !important;
  }

  /* 表头样式 */
  .header {
    display: grid;
    grid-template-rows: auto auto;
    grid-template-columns: repeat(10, 1fr);
    font-weight: bold;
    color: white;
    background: linear-gradient(135deg, #007bff, #1e90ff);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .header > div:first-child {
    grid-column: 1 / span 11;
    display: grid;
    grid-template-columns: 0.5fr 1fr 1fr 1fr 4fr 1fr 1fr;

    .span-3 {
      border-left: 1px solid #eaeaea;
      border-right: 1px solid #eaeaea;
    }
  }

  // 表头宽
  .header > div:last-child {
    grid-column: 1 / span 10;
    display: grid;
    grid-template-columns: 0.5fr repeat(9, 1fr);
  }

  /* 定义每一列的宽度 */
  .grid-table > div {
    display: grid;
    grid-template-columns: 0.5fr repeat(9, 1fr);
    padding: 10px;
    text-align: center;
    border-right: 1px solid #eaeaea;
    transition: background-color 0.2s;

    &:last-child {
      border-right: none;
    }
  }

  /* 单元格悬停效果 */
  .grid-table > div:hover {
    background-color: #e9ecef;
  }

  /* 小计单元格样式 */
  .subtotal .span-3 {
    grid-column: span 3; /* 跨越3列 */
    background-color: #e6ffed;
    color: #28a745;
    font-weight: bold;
  }

  .span-8 {
    grid-column: span 7; /* 跨越8列 */
    background-color: #e6ffed;
    color: #28a745;
    font-weight: bold;
  }

  /* 小计行项目名称样式 */
  .subtotal > div:first-child {
    font-weight: bold;
  }

  /* 行交替背景色 */
  .grid-table > :nth-child(even) {
    background-color: #f8f9fa;
  }

  .grid-table > :nth-child(odd) {
    background-color: #ffffff;
  }
</style>
